# Privacy AI Assistant - Python Backend Requirements

# Core web framework
fastapi>=0.100.0,<0.120.0
uvicorn>=0.20.0,<0.40.0

# WebSocket support
websockets>=11.0,<16.0

# Audio processing
sounddevice>=0.4.0,<0.6.0
numpy>=1.20.0,<2.0.0
pydub>=0.25.0,<0.26.0
audioop-lts>=0.2.0,<0.3.0

# Speech recognition
vosk>=0.3.40,<0.4.0

# HTTP client for Ollama
requests>=2.28.0,<3.0.0

# Data validation
pydantic>=2.0.0,<3.0.0

# System monitoring and hardware detection
psutil>=5.9.0,<6.0.0
