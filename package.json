{"name": "privacy-ai-assistant", "version": "0.1.0", "description": "Privacy-first, offline AI assistant desktop application", "type": "module", "scripts": {"dev": "tauri dev", "dev:web": "vite", "build": "vite build", "build:check": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""}, "dependencies": {"@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-fs": "^2.4.0", "@tauri-apps/plugin-http": "^2.3.0", "@tauri-apps/plugin-shell": "^2.3.0", "@tauri-apps/plugin-store": "^2.3.0", "clsx": "^2.0.0", "lucide-react": "^0.300.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "tailwind-merge": "^2.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/privacy-ai-assistant.git"}, "keywords": ["ai", "privacy", "desktop", "tauri", "react", "typescript"], "author": "Your Name", "license": "MIT"}