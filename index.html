<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/tauri.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Privacy AI Assistant</title>
    <style>
      /* Prevent flash of unstyled content */
      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', system-ui, -apple-system, sans-serif;
        background-color: #f9fafb;
        color: #111827;
      }
      
      /* Dark mode styles */
      @media (prefers-color-scheme: dark) {
        body {
          background-color: #111827;
          color: #f9fafb;
        }
      }
      
      /* Loading state */
      #root {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .loading {
        text-align: center;
        padding: 2rem;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">
        <div class="loading-spinner"></div>
        <p>Loading Privacy AI Assistant...</p>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
