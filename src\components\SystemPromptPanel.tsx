import React, { useState, useEffect } from 'react';
import { X, Save, RotateCcw, FileText, Settings, Lightbulb } from 'lucide-react';
import { cn } from '../utils/cn';
import { useSettingsStore } from '../stores/settingsStore';

interface SystemPromptPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const SystemPromptPanel: React.FC<SystemPromptPanelProps> = ({ isOpen, onClose }) => {
  const { settings, updateSystemInstructions } = useSettingsStore();
  const [currentPrompt, setCurrentPrompt] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Default system prompts for different use cases
  const presetPrompts = [
    {
      name: 'Default Assistant',
      prompt: 'You are a helpful, harmless, and honest AI assistant. Provide accurate, informative responses while being respectful and professional.',
    },
    {
      name: '<PERSON> Helper',
      prompt: 'You are an expert programming assistant. Help with code analysis, debugging, optimization, and best practices. Always explain your reasoning and provide clear, well-commented code examples.',
    },
    {
      name: 'Creative Writer',
      prompt: 'You are a creative writing assistant. Help with storytelling, character development, plot structure, and creative expression. Be imaginative and inspiring while maintaining coherence.',
    },
    {
      name: 'Research Assistant',
      prompt: 'You are a research assistant focused on accuracy and thoroughness. Provide well-sourced information, analyze data objectively, and help structure research findings clearly.',
    },
    {
      name: 'Privacy Focused',
      prompt: 'You are a privacy-first AI assistant. Always prioritize user privacy, data protection, and security. Provide guidance on digital privacy, security best practices, and data minimization.',
    }
  ];

  useEffect(() => {
    if (isOpen) {
      setCurrentPrompt(settings.systemInstructions.systemPrompt || '');
      setHasUnsavedChanges(false);
    }
  }, [isOpen, settings.systemInstructions.systemPrompt]);

  const handlePromptChange = (value: string) => {
    setCurrentPrompt(value);
    setHasUnsavedChanges(value !== (settings.systemInstructions.systemPrompt || ''));
  };

  const handleSave = () => {
    updateSystemInstructions({
      systemPrompt: currentPrompt,
      enabled: true
    });
    setHasUnsavedChanges(false);
    console.log('System prompt saved:', currentPrompt);
  };

  const handleReset = () => {
    setCurrentPrompt(settings.systemInstructions.systemPrompt || '');
    setHasUnsavedChanges(false);
  };

  const handlePresetSelect = (prompt: string) => {
    setCurrentPrompt(prompt);
    setHasUnsavedChanges(prompt !== (settings.systemInstructions.systemPrompt || ''));
  };

  const handleClear = () => {
    setCurrentPrompt('');
    setHasUnsavedChanges('' !== (settings.systemInstructions.systemPrompt || ''));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <FileText size={24} className="text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                System Prompt Editor
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Customize how the AI assistant behaves and responds
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Editor */}
            <div className="lg:col-span-2 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  System Prompt
                </label>
                <textarea
                  value={currentPrompt}
                  onChange={(e) => handlePromptChange(e.target.value)}
                  placeholder="Enter your system prompt here..."
                  className="w-full h-64 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                />
                <div className="flex items-center justify-between mt-2">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {currentPrompt.length} characters
                  </p>
                  {hasUnsavedChanges && (
                    <span className="text-sm text-orange-600 dark:text-orange-400">
                      • Unsaved changes
                    </span>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleSave}
                  disabled={!hasUnsavedChanges}
                  className={cn(
                    "flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors",
                    hasUnsavedChanges
                      ? "bg-purple-600 text-white hover:bg-purple-700"
                      : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  )}
                >
                  <Save size={16} />
                  <span>Save Changes</span>
                </button>
                
                <button
                  onClick={handleReset}
                  disabled={!hasUnsavedChanges}
                  className={cn(
                    "flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors",
                    hasUnsavedChanges
                      ? "bg-gray-600 text-white hover:bg-gray-700"
                      : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  )}
                >
                  <RotateCcw size={16} />
                  <span>Reset</span>
                </button>

                <button
                  onClick={handleClear}
                  className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <X size={16} />
                  <span>Clear</span>
                </button>
              </div>
            </div>

            {/* Preset Prompts */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Lightbulb size={20} className="text-yellow-600" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Preset Prompts
                </h3>
              </div>
              
              <div className="space-y-3">
                {presetPrompts.map((preset, index) => (
                  <div
                    key={index}
                    className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100">
                        {preset.name}
                      </h4>
                      <button
                        onClick={() => handlePresetSelect(preset.prompt)}
                        className="text-xs px-2 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
                      >
                        Use
                      </button>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-3">
                      {preset.prompt}
                    </p>
                  </div>
                ))}
              </div>

              {/* Tips */}
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Settings size={16} className="text-blue-600" />
                  <h4 className="font-medium text-sm text-blue-900 dark:text-blue-300">
                    Tips
                  </h4>
                </div>
                <ul className="text-xs text-blue-800 dark:text-blue-400 space-y-1">
                  <li>• Be specific about the AI's role and behavior</li>
                  <li>• Include guidelines for tone and style</li>
                  <li>• Mention any constraints or limitations</li>
                  <li>• Test different prompts to find what works best</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemPromptPanel;
